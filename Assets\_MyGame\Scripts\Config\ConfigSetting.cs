using System.Collections.Generic;

public class ConfigSetting : ConfigBase
{
    /// <summary>
    ///     加时(秒)
    /// </summary>
    public static int addTimeSecond;

    /// <summary>
    ///     看视频得体力数量
    /// </summary>
    public static int freeHeartCount;

    /// <summary>
    ///     没消除多久出现道具tip
    /// </summary>
    public static int idleTimeForItemTip;

    /// <summary>
    ///     没消除多久出现双击提示(仅在第二关)
    /// </summary>
    public static int idleTimeForDoubleClickTip;

    /// <summary>
    ///     1个爆竹可消除几对
    /// </summary>
    public static int firecrackerMatchCount;

    /// <summary>
    ///     机器人自动操作
    /// </summary>
    public static bool isAutoClickEnabled;

    /// <summary>
    /// 是否显示作弊工具
    /// </summary>
    public static bool isShowGmTool;
    /// <summary>
    /// 初始相邻对误差
    /// </summary>
    public static int neighborPairOffset;

    /// <summary>
    /// combo音效播放阈值（combo索引大于等于此值才播放音效，索引从1开始）
    /// </summary>
    public static int comboSoundThreshold;

    /// <summary>
    /// 锤子道具消除对数
    /// </summary>
    public static int hammerMatchCount;

    /// <summary>
    /// 结算奖励主题图案个数
    /// </summary>
    public static int rewardThemeIconCount;

    /// <summary>
    /// 折扣礼品触发进度阈值(百分比)
    /// </summary>
    public static int discountGiftProgressThreshold;

    /// <summary>
    /// 折扣礼品触发未操作时间(秒)
    /// </summary>
    public static int discountGiftIdleTime;

    /// <summary>
    /// 折扣礼品显示时间(秒)
    /// </summary>
    public static int discountGiftShowTime;

    /// <summary>
    /// 折扣礼品奖励个数
    /// </summary>
    public static int discountGiftRewardCount;

    /// <summary>
    /// 购买提示道具获得几个
    /// </summary>
    public static int buyPromptItemCount;

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        var infoSetting = info as InfoSetting;
        var id = int.Parse(key.ToString());
        switch (id)
        {
            case 1:
                addTimeSecond = infoSetting.paramInt;
                break;
            case 2:
                freeHeartCount = infoSetting.paramInt;
                break;
            case 3:
                idleTimeForItemTip = infoSetting.paramInt;
                break;
            case 4:
                firecrackerMatchCount = infoSetting.paramInt;
                break;
            case 5:
                // ParseNeighborPercentConfig(infoSetting.paramString);
                break;
            case 6:
                isAutoClickEnabled = infoSetting.paramInt == 1;
                break;
            case 7:
#if UNITY_EDITOR
                isShowGmTool = true;
#else
                isShowGmTool = infoSetting.paramInt == 1;
#endif
                break;
            case 8:
                neighborPairOffset = infoSetting.paramInt;
                break;
            case 9:
                hammerMatchCount = infoSetting.paramInt;
                break;
            case 10:
                idleTimeForDoubleClickTip = infoSetting.paramInt;
                break;
            case 11:
                rewardThemeIconCount = infoSetting.paramInt;
                break;
            case 12:
                // 解析折扣礼品配置参数，格式：进度阈值|未操作时间|显示时间|奖励个数
                var discountParams = infoSetting.paramString.Split('|');
                if (discountParams.Length >= 4)
                {
                    int.TryParse(discountParams[0], out discountGiftProgressThreshold);
                    int.TryParse(discountParams[1], out discountGiftIdleTime);
                    int.TryParse(discountParams[2], out discountGiftShowTime);
                    int.TryParse(discountParams[3], out discountGiftRewardCount);
                }
                break;
            case 13:
                comboSoundThreshold = infoSetting.paramInt;
                break;
            case 14:
                buyPromptItemCount = infoSetting.paramInt;
                break;
        }
    }


}
