using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;
[Serializable]
public class InfoGate : ConfigInfoBase
{
    public int id;
    public int gate;
    public int type;
    public int gameTime;
    public bool needEnterLobby;
    public int star;
    public int width;
    public int height;
    public int[] boardInfo;
    public int[] typeCounts;
    public int[] specialPairs;//[tileType,count,tileType,count]
    public int gold;
    public int limitLife;
    public GravityDirection gravityDirection;
    public int gateEntry;
    public int matchingPairCount;

    [SerializeField] private string neighborPercent;
    // public bool isBoss;
    /// <summary>
    ///     剩余砖块百分比对应的相邻对数百分比配置
    ///     格式：剩余数百分比,对数百分比|剩余数百分比,对数百分比|...
    ///     例如：0.8,0.5|0.6,0.5|0.4,0.6|0.2,0.7|0.1,0.8   小于等于0.8取0.5，小于等于0.4取0.6......
    /// </summary>
    private static Dictionary<float, float> neighborPercentByRemaining = new Dictionary<float, float>();

    public override object GetKey(int index)
    {
        return gate + "_" + type;
    }

    public InfoGate Clone()
    {
        InfoGate clone = new()
        {
            id = id,
            gate = gate,
            type = type,
            gameTime = gameTime,
            needEnterLobby = needEnterLobby,
            star = star,
            width = width,
            height = height,
            boardInfo = boardInfo,
            typeCounts = typeCounts,
            specialPairs = specialPairs,
            gold = gold,
            limitLife = limitLife,
            gravityDirection = gravityDirection,
            gateEntry = gateEntry,
            matchingPairCount = matchingPairCount,
            neighborPercent = neighborPercent
            // isBoss = isBoss
        };
        return clone;
    }

    public bool IsGuide()
    {
        return type == BattleType.Normal.GetHashCode() && gate == 1;
    }
    
    /// <summary>
    /// 解析剩余砖块百分比对应的相邻对数百分比配置
    /// </summary>
    /// <param name="configStr">配置字符串，格式：剩余数百分比,对数百分比|剩余数百分比,对数百分比|...</param>
    private void ParseNeighborPercentConfig(string configStr)
    {
        if (string.IsNullOrEmpty(configStr))
            return;

        neighborPercentByRemaining.Clear();
        string[] pairs = configStr.Split('|');
        foreach (var pair in pairs)
        {
            string[] values = pair.Split(',');
            if (values.Length == 2 &&
                float.TryParse(values[0], out float remainingPercent) &&
                float.TryParse(values[1], out float neighborPercent))
            {
                neighborPercentByRemaining[remainingPercent] = neighborPercent;
            }
        }
    }

    /// <summary>
    /// 根据剩余砖块百分比获取相邻对数百分比
    /// </summary>
    /// <param name="remainingPercent">剩余砖块百分比（0-1之间的值）</param>
    /// <returns>相邻对数百分比</returns>
    public float GetNeighborPercentByRemaining(float remainingPercent)
    {
        // 如果配置为空或没有配置项，返回默认值
        if (neighborPercentByRemaining.Count == 0)
        {
            ParseNeighborPercentConfig(neighborPercent);
        }

        List<float> sortedKeys = new List<float>(neighborPercentByRemaining.Keys);
        sortedKeys.Sort((a, b) => a.CompareTo(b)); // 从小到大排序

        foreach (var key in sortedKeys)
        {
            if (remainingPercent <= key)
            {
                return neighborPercentByRemaining[key];
            }
        }

        return 0;
    }

    public override void Parse(JsonData json)
    {
        id = JsonUtil.ToInt(json, "id");
        gate = JsonUtil.ToInt(json, "gate");
        type = JsonUtil.ToInt(json, "type");
        gameTime = JsonUtil.ToInt(json, "gameTime");
        needEnterLobby = JsonUtil.ToInt(json, "needEnterLobby") == 1;
        star = JsonUtil.ToInt(json, "star");
        gateEntry = JsonUtil.ToInt(json, "gateEntry");
        matchingPairCount = JsonUtil.ToInt(json, "matchingPairCount");

        #region 特殊对的处理
        //新增的特殊对加这里
        var firecrackerPairs = JsonUtil.ToInt(json, "firecrackerPairs");
        var specialPairsList = new List<int>();

        if (firecrackerPairs > 0)
        {
            specialPairsList.Add(Tile3D.TYPE_FIRECRACKER);
            specialPairsList.Add(firecrackerPairs);
        }
        neighborPercent  = JsonUtil.ToString(json, "neighborPercent");

        typeCounts = new int[5];
        typeCounts[0] = JsonUtil.ToInt(json, "typeCount2");
        typeCounts[1] = JsonUtil.ToInt(json, "typeCount4");
        typeCounts[2] = JsonUtil.ToInt(json, "typeCount6");
        typeCounts[3] = JsonUtil.ToInt(json, "typeCount8");
        typeCounts[4] = JsonUtil.ToInt(json, "typeCount10");
        #endregion

        var boardInfoStr = JsonUtil.ToString(json, "boardInfo");
        var boardGroupMap = new Dictionary<int, List<int>>(); // (groupId,type List) 用于存储相同类型的位置索引
        if (!string.IsNullOrEmpty(boardInfoStr))
        {
            var boardInfoAry = boardInfoStr.Split(",");

            width = int.Parse(boardInfoAry[0]);
            height = int.Parse(boardInfoAry[1]);

            var startIndex = 2;
            if (boardInfoAry.Length > startIndex)
            {
                boardInfo = new int[boardInfoAry.Length - startIndex];
                for (int i = startIndex; i < boardInfoAry.Length; i++)
                {
                    var type = int.Parse(boardInfoAry[i]);
                    var pos = i - startIndex;
                    boardInfo[pos] = type;

                    bool isSpecialTile = false;
                    for (int j = 0; j < specialPairsList.Count; j += 2)
                    {
                        if (specialPairsList[j] == type)
                        {
                            isSpecialTile = true;
                            break;
                        }
                    }

                    if (type > Tile3D.TYPE_BLOCK && type <= Tile3D.TYPE_BLOCK_END || isSpecialTile)
                    {
                        //类型分组存储位置
                        var groupId = type;
                        if (!boardGroupMap.ContainsKey(groupId))
                        {
                            boardGroupMap[groupId] = new List<int>();
                        }
                        boardGroupMap[groupId].Add(pos);
                    }
                }
            }
            else
            {
                boardInfo = new int[0];
            }
        }
        else
        {
            boardInfo = new int[0];
        }

        gold = JsonUtil.ToInt(json, "gold");
        gravityDirection = (GravityDirection)JsonUtil.ToInt(json, "gravityDirection");
        // isBoss = JsonUtil.ToInt(json, "isBoss") == 1;
        limitLife = JsonUtil.ToInt(json, "limitLife");


        #region 数据校验
        if (IsGuide())
            return;

        //根据board数据，更新typeCounts和specialPairs
        int groupIdx = 0;
        foreach (var group in boardGroupMap)
        {
            var type = group.Key;
            //数量不能为单数
            if (group.Value.Count % 2 != 0)
            {
                Log.Error($"[InfoGate] id:{gate} boardInfo类型({(groupIdx + 1) * 2})数量为单数{group.Value[0]}");
                return;
            }

            //typeCounts中的数量包含了board中的数量，需要减去，剩下的才是需要生成的数量
            if (type <= Tile3D.TYPE_BLOCK_END)
            {
                var index = group.Value.Count / 2 - 1;
                if (index < typeCounts.Length)
                {
                    typeCounts[index] -= 1;
                    if (typeCounts[index] < 0)
                    {
                        Log.Error($"[InfoGate] id:{gate} boardInfo类型({group.Value[0]})数量超过配置数量");
                    }
                }
                else
                {
                    Log.Error($"[InfoGate] id:{gate} boardInfo类型({group.Value[0]})找不到(类型数量{group.Value.Count})");
                }
            }
            else
            {
                bool findedPair = false;
                //更新特殊对数量
                for (int i = 0; i < specialPairsList.Count; i += 2)
                {
                    var specialTileType = specialPairsList[i];
                    if (specialTileType == type)
                    {
                        findedPair = true;
                        var specialTilePairCount = specialPairsList[i + 1];
                        var pairCount = group.Value.Count / 2;
                        if (pairCount > specialTilePairCount)
                        {
                            Log.Error($"[InfoGate] id:{gate} specialPairs类型({type})数量超过配置数量");
                            continue;
                        }
                        specialPairsList[i + 1] -= pairCount;
                        if (specialPairsList[i + 1] == 0)
                        {
                            specialPairsList.RemoveAt(i);
                            specialPairsList.RemoveAt(i);
                            i -= 2;
                        }
                    }
                }
                if (!findedPair)
                {
                    Log.Error($"[InfoGate] id:{gate} boardInfo类型({type})数量超过配置数量");
                }
            }
            groupIdx++;
        }
        specialPairs = specialPairsList.ToArray();

        // for (int i = 0; i < typeCounts.Length; i++)
        // {
        //     if (typeCounts[i] < 0)
        //     {
        //         var createCount = (i + 1) * 2;//2,4,6,8,10
        //         Log.Error($"[InfoGate] id:{gate} (类型数量{createCount}) 与boardInfo数据不匹配");
        //     }
        // }

        if (width * height % 2 != 0)
        {
            Log.Error($"[InfoGate] id:{id} width * height = {width * height} 不是偶数");
        }

        var boardTypeCount = 0;
        for (int i = 0; i < boardInfo.Length; i++)
        {
            var type = boardInfo[i];
            var needCreate = type == Tile3D.TYPE_BLOCK || type == Tile3D.TYPE_QUESTION || type == Tile3D.TYPE_CHAIN;
            if (needCreate)
            {
                boardTypeCount++;
            }
        }

        var totalCount = 0;
        var typeCount = 0;
        for (int i = 0; i < typeCounts.Length; i++)
        {
            typeCount += typeCounts[i];
            totalCount += typeCounts[i] * (i + 1) * 2;
        }

        if (typeCount > 36)
        {
            Log.Error($"[InfoGate]  id:{id} 类型总数超过36({typeCount})");
        }

        for (int i = 0; i < specialPairs.Length; i += 2)
        {
            totalCount += specialPairs[i + 1] * 2;
        }

        if (boardTypeCount > 0)
        {
            if (boardTypeCount != totalCount)
            {
                Log.Error($"[InfoGate]  id:{id} 类型数量不匹配 boardInfo:{boardTypeCount}  typeCounts:{totalCount}");
            }
        }
        else
        {
            if (totalCount != width * height)
            {
                Log.Error($"[InfoGate]  id:{id} 数量不匹配 需要数量:{width * height}  设置数量:{totalCount}");
            }
        }

        if (matchingPairCount > width * height / 2)
        {
            Log.Error($"[InfoGate]  id:{id} 匹配组数量超过格子数量");
        }
    }
    #endregion
}